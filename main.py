import streamlit as st
import openai
import requests
import os
import tempfile
import base64
from dotenv import load_dotenv
from typing import Optional, Dict, Any
import time

# Load environment variables
# Try to load from current directory first, then from script directory
load_dotenv()
if not os.getenv("OPENAI_API_KEY"):
    # Try loading from the script's directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    env_path = os.path.join(script_dir, '.env')
    load_dotenv(env_path)

class GPTClient:
    """OpenAI GPT client with error handling and configurable models."""

    def __init__(self):
        self.api_key = os.getenv("OPENAI_API_KEY")
        # Debug: Show what we're getting from environment
        if not self.api_key:
            st.error("OpenAI API key not found. Please set OPENAI_API_KEY in your .env file.")
            st.error(f"Current working directory: {os.getcwd()}")
            st.error(f"Environment variables loaded: {list(os.environ.keys())}")
            st.stop()
        elif self.api_key.startswith("your_"):
            st.error(f"OpenAI API key appears to be a placeholder: {self.api_key[:20]}...")
            st.error("Please update your .env file with a real OpenAI API key.")
            st.stop()

        self.client = openai.OpenAI(api_key=self.api_key)
        self.available_models = [
            "gpt-3.5-turbo",
            "gpt-4",
            "gpt-4-turbo-preview",
            "gpt-4o",
            "gpt-4o-mini"
        ]

    def generate_response(self, messages: list, model: str = "gpt-3.5-turbo",
                         max_tokens: int = 1000, temperature: float = 0.7) -> Optional[str]:
        """Generate a response using OpenAI GPT."""
        try:
            response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature
            )
            return response.choices[0].message.content
        except openai.APIError as e:
            st.error(f"OpenAI API Error: {e}")
            return None
        except Exception as e:
            st.error(f"Unexpected error: {e}")
            return None

class ElevenLabsClient:
    """ElevenLabs text-to-speech client with voice selection and error handling."""

    def __init__(self):
        self.api_key = os.getenv("ELEVENLABS_API_KEY")
        if not self.api_key:
            st.error("ElevenLabs API key not found. Please set ELEVENLABS_API_KEY in your .env file.")
            st.stop()

        self.base_url = "https://api.elevenlabs.io/v1"
        self.available_voices = {
            "Rachel": "21m00Tcm4TlvDq8ikWAM",
            "Drew": "29vD33N1CtxCmqQRPOHJ",
            "Clyde": "2EiwWnXFnvU5JabPnv8n",
            "Paul": "5Q0t7uMcjvnagumLfvZi",
            "Domi": "AZnzlk1XvdvUeBnXmlld",
            "Dave": "CYw3kZ02Hs0563khs1Fj",
            "Fin": "D38z5RcWu1voky8WS1ja",
            "Sarah": "EXAVITQu4vr4xnSDxMaL",
            "Antoni": "ErXwobaYiN019PkySvjV",
            "Thomas": "GBv7mTt0atIp3Br8iCZE",
            "Charlie": "IKne3meq5aSn9XLyUdCD",
            "George": "JBFqnCBsd6RMkjVDRZzb",
            "Emily": "LcfcDJNUP1GQjkzn1xUU",
            "Elli": "MF3mGyEYCl7XYWbV9V6O",
            "Callum": "N2lVS1w4EtoT3dr4eOWO",
            "Patrick": "ODq5zmih8GrVes37Dizd",
            "Harry": "SOYHLrjzK2X1ezoPC6cr",
            "Liam": "TX3LPaxmHKxFdv7VOQHJ",
            "Dorothy": "ThT5KcBeYPX3keUQqHPh",
            "Josh": "TxGEqnHWrfWFTfGW9XjX",
            "Arnold": "VR6AewLTigWG4xSOukaG",
            "Adam": "pNInz6obpgDQGcFmaJgB",
            "Sam": "yoZ06aMxZJJ28mfd3POQ",
            "Custom": os.getenv("DEFAULT_VOICE_ID", "Xb7hH8MSUJpSbSDYk0k2")
        }

    def text_to_speech(self, text: str, voice_id: str,
                      stability: float = 0.5, similarity_boost: float = 0.75) -> Optional[bytes]:
        """Convert text to speech using ElevenLabs API."""
        try:
            response = requests.post(
                f"{self.base_url}/text-to-speech/{voice_id}",
                headers={
                    "xi-api-key": self.api_key,
                    "Content-Type": "application/json"
                },
                json={
                    "text": text,
                    "model_id": "eleven_monolingual_v1",
                    "voice_settings": {
                        "stability": stability,
                        "similarity_boost": similarity_boost
                    }
                }
            )

            if response.status_code == 200:
                content_type = response.headers.get("Content-Type", "")
                if "audio" in content_type:
                    return response.content
                else:
                    st.error(f"Unexpected content type: {content_type}")
                    return None
            else:
                st.error(f"ElevenLabs API Error: {response.status_code}")
                try:
                    error_details = response.json()
                    st.error(f"Error details: {error_details}")
                except:
                    st.error(f"Response: {response.text}")
                return None

        except Exception as e:
            st.error(f"Error in text-to-speech conversion: {e}")
            return None

def create_audio_player(audio_content: bytes) -> str:
    """Create an HTML audio player for the given audio content."""
    audio_base64 = base64.b64encode(audio_content).decode()
    audio_html = f"""
    <audio controls autoplay>
        <source src="data:audio/mp3;base64,{audio_base64}" type="audio/mp3">
        Your browser does not support the audio element.
    </audio>
    """
    return audio_html

def initialize_session_state():
    """Initialize Streamlit session state variables."""
    if "messages" not in st.session_state:
        st.session_state.messages = []
    if "gpt_client" not in st.session_state:
        st.session_state.gpt_client = GPTClient()
    if "elevenlabs_client" not in st.session_state:
        st.session_state.elevenlabs_client = ElevenLabsClient()

def main():
    """Main Streamlit application."""
    st.set_page_config(
        page_title="Ask Ed GPT - Conversational AI",
        page_icon="🤖",
        layout="wide"
    )

    # Initialize session state
    initialize_session_state()

    # Sidebar for configuration
    with st.sidebar:
        st.title("⚙️ Configuration")

        # Model selection
        selected_model = st.selectbox(
            "Select GPT Model:",
            st.session_state.gpt_client.available_models,
            index=0
        )

        # Voice selection
        selected_voice_name = st.selectbox(
            "Select Voice:",
            list(st.session_state.elevenlabs_client.available_voices.keys()),
            index=0
        )
        selected_voice_id = st.session_state.elevenlabs_client.available_voices[selected_voice_name]

        # Advanced settings
        st.subheader("Advanced Settings")
        temperature = st.slider("Temperature (Creativity):", 0.0, 2.0, 0.7, 0.1)
        max_tokens = st.slider("Max Tokens:", 100, 2000, 1000, 100)

        # Voice settings
        st.subheader("Voice Settings")
        stability = st.slider("Voice Stability:", 0.0, 1.0, 0.5, 0.1)
        similarity_boost = st.slider("Voice Similarity:", 0.0, 1.0, 0.75, 0.05)

        # Clear conversation button
        if st.button("🗑️ Clear Conversation"):
            st.session_state.messages = []
            st.rerun()

    # Main chat interface
    st.title("🤖 Ask Ed GPT - Conversational AI")
    st.markdown("Chat with AI and hear responses with realistic voice synthesis!")

    # Display chat messages
    for message in st.session_state.messages:
        with st.chat_message(message["role"]):
            st.markdown(message["content"])
            if message["role"] == "assistant" and "audio" in message:
                st.markdown("🔊 **Audio Response:**")
                st.markdown(message["audio"], unsafe_allow_html=True)

    # Chat input
    if prompt := st.chat_input("Type your message here..."):
        # Add user message to chat history
        st.session_state.messages.append({"role": "user", "content": prompt})

        # Display user message
        with st.chat_message("user"):
            st.markdown(prompt)

        # Generate AI response
        with st.chat_message("assistant"):
            with st.spinner("🤔 Thinking..."):
                # Prepare messages for GPT
                gpt_messages = [{"role": msg["role"], "content": msg["content"]}
                              for msg in st.session_state.messages]

                # Generate response
                response = st.session_state.gpt_client.generate_response(
                    messages=gpt_messages,
                    model=selected_model,
                    max_tokens=max_tokens,
                    temperature=temperature
                )

                if response:
                    st.markdown(response)

                    # Generate audio
                    with st.spinner("🎵 Generating audio..."):
                        audio_content = st.session_state.elevenlabs_client.text_to_speech(
                            text=response,
                            voice_id=selected_voice_id,
                            stability=stability,
                            similarity_boost=similarity_boost
                        )

                        if audio_content:
                            audio_html = create_audio_player(audio_content)
                            st.markdown("🔊 **Audio Response:**")
                            st.markdown(audio_html, unsafe_allow_html=True)

                            # Add assistant message with audio to chat history
                            st.session_state.messages.append({
                                "role": "assistant",
                                "content": response,
                                "audio": audio_html
                            })
                        else:
                            # Add assistant message without audio to chat history
                            st.session_state.messages.append({
                                "role": "assistant",
                                "content": response
                            })
                            st.warning("Audio generation failed, but text response is available.")
                else:
                    st.error("Failed to generate response. Please try again.")

if __name__ == "__main__":
    main()
