import requests

# Replace with your actual ElevenLabs API key
API_KEY = "***************************************************"
# Ensure the correct Voice ID is used. Double-check the ElevenLabs documentation.
VOICE_ID = "Xb7hH8MSUJpSbSDYk0k2"  # Example: This should be the correct voice ID from their library
text = "Hello, this is your custom GPT talking with ElevenLabs!"

# Send the request to ElevenLabs API
response = requests.post(
    f"https://api.elevenlabs.io/v1/text-to-speech/{VOICE_ID}",
    headers={
        "xi-api-key": API_KEY,
        "Content-Type": "application/json"
    },
    json={
        "text": text,
        "model_id": "eleven_monolingual_v1",  # You can use other model options
        "voice_settings": {
            "stability": 0.5,
            "similarity_boost": 0.75
        }
    }
)

# Check the response status code and if it's a valid audio file
if response.status_code == 200:
    # Ensure the content is audio and not an error message
    content_type = response.headers.get("Content-Type")
    
    if "audio" in content_type:
        # Save the audio to a file
        with open("output_audio.mp3", "wb") as f:
            f.write(response.content)
        print("Audio file has been saved successfully!")
    else:
        # If it's not audio, print the error message
        print(f"Error: Received unexpected content type: {content_type}")
        print(response.json())  # Print the error response
else:
    # Print the error code and the response from ElevenLabs if the request fails
    print(f"Error: Request failed with status code {response.status_code}")
    print(response.json())  # Print the detailed error message from ElevenLabs
